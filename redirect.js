/**
 * @fileoverview
 * This script runs on https://app.augmentcode.com/account/subscription.
 * It generates the necessary PKCE parameters, saves the verifier,
 * and redirects the user to the Augment Code auth page.
 */

// Helper function to encode an ArrayBuffer into a Base64URL string.
function base64URLEncode(buffer) {
    const base64 = btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)));
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}

/**
 * Main function to perform the authentication flow.
 */
async function initiateAuthRedirect() {
    try {
        // 1. Generate code verifier.
        const randomBytes = crypto.getRandomValues(new Uint8Array(32));
        const codeVerifier = base64URLEncode(randomBytes);

        // 2. Save the code verifier to local storage for the next step.
        // This is crucial because the value is lost after redirection.
        await chrome.storage.local.set({ codeVerifier: codeVerifier });
        console.log("Code Verifier已生成并保存。");

        // 3. Create the code challenge.
        const encoder = new TextEncoder();
        const data = encoder.encode(codeVerifier);
        const digest = await crypto.subtle.digest('SHA-256', data);
        const codeChallenge = base64URLEncode(new Uint8Array(digest));

        // 4. Generate state UUID.
        const state = crypto.randomUUID();

        // 5. Construct the authentication URL.
        const params = new URLSearchParams({
            response_type: 'code',
            code_challenge: codeChallenge,
            code_challenge_method: 'S256',
            client_id: 'augment-vscode-extension',
            redirect_uri: 'vscode://augment.vscode-augment/auth/result',
            state: state,
            scope: 'email',
            prompt: 'login'
        });
        const authUrl = `https://auth.augmentcode.com/terms-accept?${params.toString()}`;

        // 6. Redirect to the auth URL.
        console.log("正在跳转至认证页面...");
        window.location.href = authUrl;

    } catch (error) {
        console.error("生成认证参数或跳转时发生错误:", error);
        alert("无法自动跳转到认证页面，请查看控制台获取错误信息。");
    }
}

initiateAuthRedirect();
