/**
 * @fileoverview
 * This script runs on https://auth.augmentcode.com/*
 * It searches for the vscode:// link and retrieves the codeVerifier from storage,
 * then displays both prominently for the user.
 */

/**
 * Searches the document's body for a string matching the VS Code auth link pattern.
 * @returns {string|null} The found link or null if not found.
 */
function findVscodeLink() {
    const linkRegex = /(vscode:\/\/augment\.vscode-augment\/auth\/result\?[^\s"'<>]+)/;
    const match = document.body.innerHTML.match(linkRegex);

    if (match && match[0]) {
        // Decode HTML entities like &amp; to &
        const decodedLink = match[0].replace(/&amp;/g, '&');
        console.log("成功找到并解码VS Code链接:", decodedLink);
        return decodedLink;
    }
    console.log("未在页面中找到VS Code链接。");
    return null;
}

/**
 * Retrieves the codeVerifier from Chrome storage.
 * @returns {Promise<string|null>} The code verifier or null if not found.
 */
async function getCodeVerifier() {
    try {
        const result = await chrome.storage.local.get(['codeVerifier']);
        if (result.codeVerifier) {
            console.log("成功从存储中获取codeVerifier:", result.codeVerifier);
            return result.codeVerifier;
        } else {
            console.log("存储中未找到codeVerifier。");
            return null;
        }
    } catch (error) {
        console.error("读取Chrome存储时发生错误:", error);
        return null;
    }
}

/**
 * Retrieves data from auth.augmentcode.com using cross-domain techniques.
 * @returns {Promise<Object|null>} Auth data or null if not accessible.
 */
async function getAuthData() {
    try {
        console.log("尝试从 auth.augmentcode.com 获取数据...");

        // Method 1: Try direct fetch (will work if CORS is properly configured)
        try {
            const response = await fetch('https://auth.augmentcode.com', {
                method: 'GET',
                credentials: 'include', // Include cookies
                headers: {
                    'Accept': 'application/json, text/html, */*'
                }
            });

            if (response.ok) {
                const data = await response.text();
                console.log("成功从 auth.augmentcode.com 获取数据");
                return { source: 'auth.augmentcode.com', data: data };
            }
        } catch (fetchError) {
            console.log("直接fetch失败，尝试其他方法:", fetchError.message);
        }

        // Method 2: Try to access via iframe (if same-origin policy allows)
        return await getDataViaIframe('https://auth.augmentcode.com');

    } catch (error) {
        console.error("从 auth.augmentcode.com 获取数据时发生错误:", error);
        return null;
    }
}

/**
 * Retrieves data from app.augmentcode.com using cross-domain techniques.
 * @returns {Promise<Object|null>} App data or null if not accessible.
 */
async function getAppData() {
    try {
        console.log("尝试从 app.augmentcode.com 获取数据...");

        // Method 1: Try direct fetch
        try {
            const response = await fetch('https://app.augmentcode.com', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json, text/html, */*'
                }
            });

            if (response.ok) {
                const data = await response.text();
                console.log("成功从 app.augmentcode.com 获取数据");
                return { source: 'app.augmentcode.com', data: data };
            }
        } catch (fetchError) {
            console.log("直接fetch失败，尝试其他方法:", fetchError.message);
        }

        // Method 2: Try to access via iframe
        return await getDataViaIframe('https://app.augmentcode.com');

    } catch (error) {
        console.error("从 app.augmentcode.com 获取数据时发生错误:", error);
        return null;
    }
}

/**
 * Attempts to retrieve data via iframe (limited by same-origin policy).
 * @param {string} url The URL to load in iframe.
 * @returns {Promise<Object|null>} Data or null if not accessible.
 */
async function getDataViaIframe(url) {
    return new Promise((resolve) => {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = url;

        const timeout = setTimeout(() => {
            document.body.removeChild(iframe);
            console.log(`iframe方法超时: ${url}`);
            resolve(null);
        }, 5000);

        iframe.onload = () => {
            try {
                // This will only work if same-origin policy allows
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const data = iframeDoc.documentElement.outerHTML;

                clearTimeout(timeout);
                document.body.removeChild(iframe);
                console.log(`成功通过iframe获取数据: ${url}`);
                resolve({ source: url, data: data, method: 'iframe' });
            } catch (error) {
                clearTimeout(timeout);
                document.body.removeChild(iframe);
                console.log(`iframe方法被同源策略阻止: ${url}`, error.message);
                resolve(null);
            }
        };

        iframe.onerror = () => {
            clearTimeout(timeout);
            document.body.removeChild(iframe);
            console.log(`iframe加载失败: ${url}`);
            resolve(null);
        };

        document.body.appendChild(iframe);
    });
}

/**
 * Creates and injects a banner at the top of the page to display the found info.
 * @param {string} link The vscode:// link to display.
 * @param {string|null} verifier The code verifier string to display.
 * @param {Object|null} authData Data from auth.augmentcode.com.
 * @param {Object|null} appData Data from app.augmentcode.com.
 */
function displayResult(link, verifier, authData, appData) {
    const banner = document.createElement('div');
    banner.id = 'vscode-link-extractor-banner';
    banner.style.cssText = `
        position: sticky; top: 0; left: 0; width: 100%;
        background-color: #1a1a1a; color: #e0e0e0; padding: 20px;
        z-index: 99999; font-family: sans-serif;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        border-bottom: 2px solid #4CAF50;
    `;

    let bannerContent = `
        <div style="max-width: 800px; margin: auto; display: grid; gap: 15px;">
            <!-- Row for VSCode Link -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #4CAF50; min-width: 120px;">✅ VSCode 链接:</strong>
                <input type="text" readonly value="${link}" id="vscode-link-input" style="flex-grow: 1; padding: 8px; border-radius: 4px; border: 1px solid #555; background-color: #333; color: #fff; font-size: 14px;">
                <button data-copy-target="vscode-link-input" class="copy-btn" style="padding: 8px 15px; border-radius: 4px; border: none; background-color: #4CAF50; color: white; cursor: pointer;">复制</button>
            </div>`;

    // Add Code Verifier section
    if (verifier) {
        bannerContent += `
            <!-- Row for Code Verifier -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #2196F3; min-width: 120px;">🔑 Code Verifier:</strong>
                <input type="text" readonly value="${verifier}" id="code-verifier-input" style="flex-grow: 1; padding: 8px; border-radius: 4px; border: 1px solid #555; background-color: #333; color: #fff; font-size: 14px;">
                <button data-copy-target="code-verifier-input" class="copy-btn" style="padding: 8px 15px; border-radius: 4px; border: none; background-color: #2196F3; color: white; cursor: pointer;">复制</button>
            </div>`;
    } else {
        bannerContent += `
            <!-- Row for Code Verifier (Not Found) -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #FF9800; min-width: 120px;">⚠️ Code Verifier:</strong>
                <span style="flex-grow: 1; padding: 8px; color: #FF9800; font-style: italic;">未在Chrome存储中找到</span>
            </div>`;
    }

    // Add Auth Data section
    if (authData && authData.data) {
        const authPreview = authData.data.length > 200 ? authData.data.substring(0, 200) + '...' : authData.data;
        bannerContent += `
            <!-- Row for Auth Data -->
            <div style="display: flex; align-items: flex-start; gap: 10px;">
                <strong style="font-size: 14px; color: #9C27B0; min-width: 120px; padding-top: 8px;">🔐 Auth Data:</strong>
                <textarea readonly id="auth-data-input" style="flex-grow: 1; padding: 8px; border-radius: 4px; border: 1px solid #555; background-color: #333; color: #fff; font-size: 12px; min-height: 60px; resize: vertical;">${authPreview}</textarea>
                <button data-copy-target="auth-data-input" class="copy-btn" style="padding: 8px 15px; border-radius: 4px; border: none; background-color: #9C27B0; color: white; cursor: pointer;">复制</button>
            </div>`;
    } else {
        bannerContent += `
            <!-- Row for Auth Data (Not Found) -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #FF5722; min-width: 120px;">❌ Auth Data:</strong>
                <span style="flex-grow: 1; padding: 8px; color: #FF5722; font-style: italic;">无法从 auth.augmentcode.com 获取数据</span>
            </div>`;
    }

    // Add App Data section
    if (appData && appData.data) {
        const appPreview = appData.data.length > 200 ? appData.data.substring(0, 200) + '...' : appData.data;
        bannerContent += `
            <!-- Row for App Data -->
            <div style="display: flex; align-items: flex-start; gap: 10px;">
                <strong style="font-size: 14px; color: #607D8B; min-width: 120px; padding-top: 8px;">📱 App Data:</strong>
                <textarea readonly id="app-data-input" style="flex-grow: 1; padding: 8px; border-radius: 4px; border: 1px solid #555; background-color: #333; color: #fff; font-size: 12px; min-height: 60px; resize: vertical;">${appPreview}</textarea>
                <button data-copy-target="app-data-input" class="copy-btn" style="padding: 8px 15px; border-radius: 4px; border: none; background-color: #607D8B; color: white; cursor: pointer;">复制</button>
            </div>`;
    } else {
        bannerContent += `
            <!-- Row for App Data (Not Found) -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #FF5722; min-width: 120px;">❌ App Data:</strong>
                <span style="flex-grow: 1; padding: 8px; color: #FF5722; font-style: italic;">无法从 app.augmentcode.com 获取数据</span>
            </div>`;
    }

    bannerContent += `
        </div>
    `;

    banner.innerHTML = bannerContent;

    document.body.insertBefore(banner, document.body.firstChild);

    // Add copy-to-clipboard functionality to all copy buttons.
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            const targetInputId = event.target.getAttribute('data-copy-target');
            const input = document.getElementById(targetInputId);
            input.select();
            navigator.clipboard.writeText(input.value).then(() => {
                const successMsg = document.getElementById('copy-success-msg');
                successMsg.style.display = 'block';
                setTimeout(() => {
                    successMsg.style.display = 'none';
                }, 2000);
            }).catch(err => {
                console.error('无法复制: ', err);
                alert('无法自动复制，请手动复制。');
            });
        });
    });
}

// Run the script after a short delay to ensure all page content is loaded.
window.setTimeout(async () => {
    console.log("VS Code链接提取器已启动");

    const vscodeLink = findVscodeLink();
    const codeVerifier = await getCodeVerifier();

    if (vscodeLink) {
        try {
            console.log("找到VS Code链接:", vscodeLink);
            displayResult(vscodeLink, codeVerifier);
        } catch (error) {
            console.error("读取存储或显示结果时发生错误:", error);
            displayResult(vscodeLink, null);
        }
    } else {
        console.log("未找到VS Code链接");
        // Even if no link is found, we can still show the verifier if it exists
        if (codeVerifier) {
            console.log("找到Code Verifier:", codeVerifier);
            displayResult("未找到VS Code链接", codeVerifier);
        }
    }
}, 500);
