/**
 * @fileoverview
 * This script runs on https://auth.augmentcode.com/*
 * It searches for the vscode:// link and retrieves the codeVerifier from storage,
 * then displays both prominently for the user.
 */

/**
 * Searches the document's body for a string matching the VS Code auth link pattern.
 * @returns {string|null} The found link or null if not found.
 */
function findVscodeLink() {
    const linkRegex = /(vscode:\/\/augment\.vscode-augment\/auth\/result\?[^\s"'<>]+)/;
    const match = document.body.innerHTML.match(linkRegex);

    if (match && match[0]) {
        // Decode HTML entities like &amp; to &
        const decodedLink = match[0].replace(/&amp;/g, '&');
        console.log("成功找到并解码VS Code链接:", decodedLink);
        return decodedLink;
    }
    console.log("未在页面中找到VS Code链接。");
    return null;
}

/**
 * Retrieves the codeVerifier from Chrome storage.
 * @returns {Promise<string|null>} The code verifier or null if not found.
 */
async function getCodeVerifier() {
    try {
        const result = await chrome.storage.local.get(['codeVerifier']);
        if (result.codeVerifier) {
            console.log("成功从存储中获取codeVerifier:", result.codeVerifier);
            return result.codeVerifier;
        } else {
            console.log("存储中未找到codeVerifier。");
            return null;
        }
    } catch (error) {
        console.error("读取Chrome存储时发生错误:", error);
        return null;
    }
}

/**
 * Creates and injects a banner at the top of the page to display the found info.
 * @param {string} link The vscode:// link to display.
 * @param {string|null} verifier The code verifier string to display.
 */
function displayResult(link, verifier) {
    const banner = document.createElement('div');
    banner.id = 'vscode-link-extractor-banner';
    banner.style.cssText = `
        position: sticky; top: 0; left: 0; width: 100%;
        background-color: #1a1a1a; color: #e0e0e0; padding: 20px;
        z-index: 99999; font-family: sans-serif;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        border-bottom: 2px solid #4CAF50;
    `;

    let bannerContent = `
        <div style="max-width: 800px; margin: auto; display: grid; gap: 15px;">
            <!-- Row for VSCode Link -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #4CAF50; min-width: 120px;">✅ VSCode 链接:</strong>
                <input type="text" readonly value="${link}" id="vscode-link-input" style="flex-grow: 1; padding: 8px; border-radius: 4px; border: 1px solid #555; background-color: #333; color: #fff; font-size: 14px;">
                <button data-copy-target="vscode-link-input" class="copy-btn" style="padding: 8px 15px; border-radius: 4px; border: none; background-color: #4CAF50; color: white; cursor: pointer;">复制</button>
            </div>`;

    // Add Code Verifier section
    if (verifier) {
        bannerContent += `
            <!-- Row for Code Verifier -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #2196F3; min-width: 120px;">🔑 Code Verifier:</strong>
                <input type="text" readonly value="${verifier}" id="code-verifier-input" style="flex-grow: 1; padding: 8px; border-radius: 4px; border: 1px solid #555; background-color: #333; color: #fff; font-size: 14px;">
                <button data-copy-target="code-verifier-input" class="copy-btn" style="padding: 8px 15px; border-radius: 4px; border: none; background-color: #2196F3; color: white; cursor: pointer;">复制</button>
            </div>`;
    } else {
        bannerContent += `
            <!-- Row for Code Verifier (Not Found) -->
            <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="font-size: 14px; color: #FF9800; min-width: 120px;">⚠️ Code Verifier:</strong>
                <span style="flex-grow: 1; padding: 8px; color: #FF9800; font-style: italic;">未在Chrome存储中找到</span>
            </div>`;
    }

    bannerContent += `
        </div>
    `;

    banner.innerHTML = bannerContent;

    document.body.insertBefore(banner, document.body.firstChild);

    // Add copy-to-clipboard functionality to all copy buttons.
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            const targetInputId = event.target.getAttribute('data-copy-target');
            const input = document.getElementById(targetInputId);
            input.select();
            navigator.clipboard.writeText(input.value).then(() => {
                const successMsg = document.getElementById('copy-success-msg');
                successMsg.style.display = 'block';
                setTimeout(() => {
                    successMsg.style.display = 'none';
                }, 2000);
            }).catch(err => {
                console.error('无法复制: ', err);
                alert('无法自动复制，请手动复制。');
            });
        });
    });
}

// Run the script after a short delay to ensure all page content is loaded.
window.setTimeout(async () => {
    const vscodeLink = findVscodeLink();

    if (vscodeLink) {
        try {
            displayResult(vscodeLink);
        } catch (error) {
            console.error("读取存储或显示结果时发生错误:", error);
            displayResult(vscodeLink, `读取存储时出错: ${error.message}`);
        }
    }
}, 500);
